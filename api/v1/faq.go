package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	model "gtcms/internal/model/islamic"
)

type CateCreateReq struct {
	g.Meta `path:"/faqCate/add" method:"post" tags:"faq" summary:"faq分类创建"`
	Item   []CateCreateItem `v:"required|array" json:"item"`
	Sort   uint             `v:"between:0,9999" json:"sort" dc:"cate 排序"`
}

type CateCreateItem struct {
	Name       string `v:"required|length:1,60" json:"name" dc:"cate 名称"`
	LanguageId int    `v:"required|in:0,1,2" json:"languageId" dc:"语言"`
}
type CateCreateRes struct {
	Id int64 `json:"id" dc:"faq_cate id"`
}

type CateEditeReq struct {
	g.Meta `path:"/faqCate/edit" method:"post" tags:"faq" summary:"faq分类修改"`
	Id     int64          `v:"required" json:"id" dc:"faq_cate id"`
	Item   []CateEditItem `v:"required|array|required" json:"item"`
	Sort   uint           `v:"required|between:0,9999" json:"sort" dc:"cate 排序"`
}

type CateEditRes struct {
	Id int64 `json:"id" dc:"faq_cate id"`
}

type CateEditItem struct {
	Id int64 `json:"id"`
	CateCreateItem
}

type CateListReq struct {
	g.Meta `path:"/faqCate/list" method:"post" tags:"faq" summary:"faq分类列表"`
	ListReq
	Title string `v:"length:1,60" json:"title" dc:"cat 名称"`
	Sort  uint   `v:"between:0,9999" json:"sort" dc:"cate 排序"`
}

type CateListRes struct {
	ListRes
	List []*model.FaqCateWithLanguage `json:"list" dc:"列表"`
}

type CateItem struct {
	entity.FaqCate
}

type CateOneReq struct {
	g.Meta `path:"/faqCate/one" method:"post" tags:"faq" summary:"faq分类详情"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type CateOneRes struct {
	model.FaqCateWithLanguage
}

type CateDeleteReq struct {
	g.Meta `path:"/faqCate/delete" method:"post" tags:"faq" summary:"faq分类删除"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type CateDeleteRes struct {
}

type QuestionCreateReq struct {
	g.Meta     `path:"/faq/add" method:"post" tags:"faq" summary:"faq新增"`
	CateId     uint                  `v:"required" json:"cateId" dc:"分类id"`
	Sort       uint                  `v:"required|between:0,9999" json:"sort" dc:"cate 排序"`
	Item       []*QuestionCreateItem `v:"required|array" json:"item" dc:"多语言item"`
	PositionId []int                 `v:"required|array" json:"positionId" dc:"位置ids"`
}

type QuestionCreateItem struct {
	Title      string `v:"required|length:1,60" json:"title" dc:"标题"`
	Desc       string `v:"required" json:"desc" dc:"描述"`
	LanguageId uint   `v:"required|in:0,1,2" json:"languageId" dc:"语言"`
}
type QuestionCreateRes struct {
	Id uint `v:"required" json:"id" dc:"id"`
}

type QuestionEditReq struct {
	g.Meta     `path:"/faq/edit" method:"post" tags:"faq" summary:"faq编辑"`
	Id         uint                `v:"required" json:"id" dc:"id"`
	CateId     uint                `v:"required" json:"cateId" dc:"分类id"`
	Sort       uint                `v:"required|between:0,9999" json:"sort" dc:"cate 排序"`
	Item       []*QuestionEditItem `v:"required|array" json:"item" dc:"多语言item"`
	PositionId []int               `v:"required|array" json:"positionId" dc:"位置ids"`
}

type QuestionEditRes struct {
	Id uint `v:"required" json:"id" dc:"id"`
}

type QuestionEditItem struct {
	Id         uint   `v:"" json:"id" dc:"id"`
	Title      string `v:"required|length:1,60" json:"title" dc:"标题"`
	Desc       string `v:"required" json:"desc" dc:"描述"`
	LanguageId uint   `v:"required|in:0,1,2" json:"languageId"`
}

type QuestionDeleteReq struct {
	g.Meta `path:"/faq/delete" method:"post" tags:"faq" summary:"faq删除"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type QuestionDeleteRes struct {
	//Id uint `v:"required" json:"id" dc:"id"`
}

type QuestionOneReq struct {
	g.Meta `path:"/faq/one" method:"post" tags:"faq" summary:"faq详情"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type QuestionOneRes struct {
	*model.FaqQuestionWithLanguage
}

type QuestionListReq struct {
	g.Meta `path:"/faq/list" method:"post" tags:"faq" summary:"faq列表"`
	ListReq
	Title  string `v:"length:1,60" json:"title" dc:"标题"`
	CateId uint   `v:"" dc:"分类id" json:"cateId"`
}

type QuestionListRes struct {
	ListRes
	List []*model.FaqQuestionWithLanguage `json:"list" dc:"列表"`
}

type FaqPublishReq struct {
	g.Meta    `path:"/faq/publish" method:"post" tags:"faq" summary:"faq发布和下线"`
	Ids       []uint `v:"required" json:"ids" dc:"ids"`
	IsPublish int    `v:"required|in:1,2" json:"isPublish" dc:"状态 (1:发布 2:下线)"`
}

type FaqPublishRes struct {
}
