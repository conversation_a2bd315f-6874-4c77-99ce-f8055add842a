package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	model "gtcms/internal/model/islamic"
)

type WisdomCateCreateReq struct {
	g.Meta `path:"/wisdomCate/add" method:"post" tags:"名言" summary:"名言分类创建"`
	Item   []WisdomCateCreateItem `v:"required|array|required" json:"item"`
	Sort   uint                   `v:"between:0,9999" json:"sort" dc:"cate 排序"`
	Remark string                 `v:"" json:"remark" dc:"备注"`
}

type WisdomCateCreateItem struct {
	LanguageId int    `v:"required|in:0,1,2" json:"languageId" dc:"语言"`
	Title      string `v:"required|length:1,60" json:"title" dc:"cate 名称"`
}

type WisdomCateCreateRes struct {
	Id int64 `json:"id" dc:"wisdom_cate id"`
}

type WisdomCateEditReq struct {
	g.<PERSON>a `path:"/wisdomCate/edit" method:"post" tags:"名言" summary:"名言分类修改"`
	Id     int64                `v:"required" json:"id" dc:"wisdom_cate id"`
	Item   []WisdomCateEditItem `v:"required|array|required" json:"item"`
	Sort   uint                 `v:"between:0,9999" json:"sort" dc:"cate 排序"`
	Remark string               `v:"" dc:"备注"`
}

type WisdomCateEditItem struct {
	WisdomCateCreateItem
}

type WisdomCateEditRes struct {
}
type WisdomCateListReq struct {
	g.Meta `path:"/wisdomCate/list" method:"post" tags:"名言" summary:"分类列表"`
	ListReq
	Title string `v:"length:1,60" json:"title" dc:"分类名称"`
}

type WisdomCateListRes struct {
	ListRes
	List []*model.WisdomCateWithLanguage `json:"list" dc:"列表"`
}

type WisdomCateOneReq struct {
	g.Meta `path:"/wisdomCate/one" method:"post" tags:"名言" summary:"分类详情"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type WisdomCateOneRes struct {
	model.WisdomCateWithLanguage
}

type WisdomCateDeleteReq struct {
	g.Meta `path:"/wisdomCate/delete" method:"post" tags:"名言" summary:"分类删除"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type WisdomCateDeleteRes struct {
}

type WisdomCreateReq struct {
	g.Meta          `path:"/wisdom/add" method:"post" tags:"名言" summary:"名言新增"`
	CateId          uint               `v:"required" json:"cateId" dc:"分类id"`
	Sort            uint               `v:"between:0,9999" json:"sort"  dc:"cate 排序"`
	ArticleId       uint               `v:"required" json:"articleId" dc:"文章id"`
	ArticleCategory uint               `v:"required" json:"articleCategory" dc:"文章分类"`
	Item            []WisdomCreateItem `v:"required|array|required" json:"item"`
}

type WisdomCreateItem struct {
	Title      string `v:"required|length:1,60" json:"title" dc:"标题"`
	ImageUrl   string `v:"required" dc:"图片url" json:"imageUrl"`
	LanguageId int    `v:"required|in:0,1,2" json:"languageId" dc:"语言"`
}
type WisdomCreateRes struct {
	Id uint `v:"required" json:"id" dc:"id"`
}

type WisdomEditReq struct {
	g.Meta          `path:"/wisdom/edit" method:"post" tags:"名言" summary:"名言修改"`
	Id              uint             `v:"required" json:"id" dc:"id"`
	CateId          uint             `v:"required" json:"cateId" dc:"分类id"`
	Sort            uint             `v:"between:0,9999" json:"sort" dc:"cate 排序"`
	ArticleId       uint             `v:"required" json:"articleId" dc:"文章id"`
	ArticleCategory uint             `v:"required" json:"articleCategory" dc:"文章分类"`
	Item            []WisdomEditItem `v:"required|array"`
}

type WisdomEditItem struct {
	WisdomCreateItem
}
type WisdomEditRes struct {
}

type WisdomDeleteReq struct {
	g.Meta `path:"/wisdom/delete" method:"post" tags:"名言" summary:"名言删除"`
	Ids    []uint `v:"required" json:"ids" dc:"ids"`
}

type WisdomDeleteRes struct {
}

type WisdomListReq struct {
	g.Meta `path:"/wisdom/list" method:"post" tags:"名言" summary:"名言列表"`
	ListReq
	CateId    uint   `v:"" json:"cateId" dc:"分类id"`
	Title     string `v:"length:1,60" json:"title" dc:"标题"`
	IsPublish *int   `v:"in:0,1,2" json:"isPublish" dc:"发布状态 0：待发布 1：已发布 2 已下线"`
	StartTime int    `v:"" json:"startTime" dc:"开始时间"`
	EndTime   int    `v:"" json:"endTime" dc:"结束时间"`
}

type WisdomListRes struct {
	ListRes
	List []*model.WisdomWithLanguage `json:"list" dc:"列表"`
}

type WisdomOneReq struct {
	g.Meta `path:"/wisdom/one" method:"post" tags:"名言" summary:"名言详情"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type WisdomOneRes struct {
	model.WisdomWithLanguage
}

type WisdomCateAllReq struct {
	g.Meta `path:"/wisdomCate/all" method:"post" tags:"名言" summary:"名言类别所有"`
	Title  string `v:"length:1,60" json:"title" dc:"分类名称"`
}
type WisdomCateAllRes struct {
	List []*model.WisdomCateWithLanguage `json:"list" dc:"列表"`
}

type WisdomPublishReq struct {
	g.Meta    `path:"/wisdom/publish" method:"post" tags:"名言" summary:"名言发布和下线"`
	Ids       []uint `v:"required" json:"ids" dc:"ids"`
	IsPublish int    `v:"required|in:1,2" json:"isPublish" dc:"状态 (1:发布 2:下线)"`
}

type WisdomPublishRes struct {
}
