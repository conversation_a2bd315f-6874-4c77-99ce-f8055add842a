package feedback

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// 列表
func (c *Controller) FeedbackList(ctx context.Context, req *v1.FeedbackListReq) (res *v1.FeedbackListRes, err error) {
	res, err = service.Feedback().FeedbackList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return

}

// 详情
func (c *Controller) FeedbackOne(ctx context.Context, req *v1.FeedbackOneReq) (res *v1.FeedbackOneRes, err error) {
	res, err = service.Feedback().FeedbackOne(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// 删除
func (c *Controller) FeedbackDelete(ctx context.Context, req *v1.FeedbackDeleteReq) (res *v1.FeedbackDeleteRes, err error) {
	res, err = service.Feedback().FeedbackDelete(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return

}

// 处理反馈
func (c *Controller) FeedbackComplete(ctx context.Context, req *v1.FeedbackCompleteReq) (res *v1.FeedbackCompleteRes, err error) {
	res, err = service.Feedback().FeedbackComplete(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}
