package faq

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	model "gtcms/internal/model/islamic"
	"gtcms/internal/service"
)

type sFaq struct{}

func init() {
	service.RegisterFaq(New())
}
func New() *sFaq {
	return &sFaq{}
}

func (f *sFaq) CateAdd(ctx context.Context, req *v1.CateCreateReq) (res *v1.CateCreateRes, err error) {
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}

	err = dao.FaqCate.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// cate表新增。
		lastId, err := tx.Model(dao.FaqCate.Table()).Ctx(ctx).
			Data(
				do.FaqCate{
					Sort:          req.Sort,
					CateCount:     0,
					CreateAccount: admin.Account,
					CreateTime:    gtime.Now().UnixMilli(),
				}).InsertAndGetId()
		if err != nil {
			return err
		}
		// cate_languages表新增。
		var faqCateLanguages = make([]do.FaqCateLanguage, 0, len(req.Item))
		isZh := 0
		isEn := 0
		isId := 0
		for _, item := range req.Item {
			faqCateLanguage := do.FaqCateLanguage{
				FaqCateId:  lastId,
				Title:      item.Name,
				LanguageId: item.LanguageId,
			}
			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}

			faqCateLanguages = append(faqCateLanguages, faqCateLanguage)
		}

		_, err = tx.Model(dao.FaqCateLanguage.Table()).Ctx(ctx).Data(faqCateLanguages).Insert()
		if err != nil {
			return err
		}

		_, err = tx.Model(dao.FaqCate.Table()).Where(dao.FaqCate.Columns().Id, lastId).Update(do.FaqCate{
			IsZh: isZh,
			IsEn: isEn,
			IsId: isId,
		})

		return err
	})

	return
}

func (f *sFaq) CateEdit(ctx context.Context, req *v1.CateEditeReq) (res *v1.CateEditRes, err error) {
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}
	err = dao.FaqCate.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 更新主表
		_, err := tx.Model(dao.FaqCate.Table()).
			Data(do.FaqCate{
				Sort:          req.Sort,
				UpdateTime:    gtime.Now().UnixMilli(),
				UpdateAccount: admin.Account,
			}).Where(dao.FaqCate.Columns().Id, req.Id).
			Update()
		if err != nil {
			return err
		}
		isZh := 0
		isEn := 0
		isId := 0
		// 2. 处理子表：新增 or 更新
		for _, item := range req.Item {
			faqCateLanguage := do.FaqCateLanguage{
				FaqCateId:  req.Id,
				Title:      item.Name,
				LanguageId: item.LanguageId,
			}
			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
			if item.Id > 0 {
				// 更新
				_, err = tx.Model(dao.FaqCateLanguage.Table()).
					Data(faqCateLanguage).
					Where(dao.FaqCateLanguage.Columns().Id, item.Id).
					Update()
			} else {
				// 新增
				_, err = tx.Model(dao.FaqCateLanguage.Table()).
					Data(faqCateLanguage).
					Insert()
			}
			if err != nil {
				return err
			}
		}

		_, err = tx.Model(dao.FaqCate.Table()).Where(dao.FaqCate.Columns().Id, req.Id).Update(do.FaqCate{
			IsZh: isZh,
			IsEn: isEn,
			IsId: isId,
		})

		return err
	})

	return
}

func (f *sFaq) CateDelete(ctx context.Context, req *v1.CateDeleteReq) (res *v1.CateDeleteRes, err error) {
	err = dao.FaqCate.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		count := 0
		count, err = tx.Model(dao.FaqQuestion.Table()).Where(dao.FaqQuestion.Columns().FaqCateId, req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			err = gerror.NewCode(gcode.CodeInvalidOperation, gi18n.T(ctx, "faqCate.delete.InvalidOperation"))
			return err
		}
		_, err = tx.Model(dao.FaqCate.Table()).Where(dao.FaqCate.Columns().Id, req.Id).Delete()

		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqCateLanguage.Table()).Where(dao.FaqCateLanguage.Columns().FaqCateId, req.Id).Delete()

		return err
	})
	return
}

func (f *sFaq) CateOne(ctx context.Context, req *v1.CateOneReq) (res *v1.CateOneRes, err error) {
	var cate model.FaqCateWithLanguage
	err = dao.FaqCate.Ctx(ctx).
		Where(dao.FaqCate.Columns().Id, req.Id).
		With(model.FaqCateWithLanguage{}.Item). // 预加载一对多
		Scan(&cate)
	if err != nil {
		return
	}
	return &v1.CateOneRes{
		FaqCateWithLanguage: cate,
	}, nil
}

func (f *sFaq) CateList(ctx context.Context, req *v1.CateListReq) (res *v1.CateListRes, err error) {
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	var cates []*model.FaqCateWithLanguage
	var total int
	md := dao.FaqCate.Ctx(ctx)
	if req.Title != "" {
		ids := dao.FaqCateLanguage.Ctx(ctx).
			WhereLike(dao.FaqCateLanguage.Columns().Title, fmt.Sprintf("%%%s%%", req.Title)).
			Fields(dao.FaqCateLanguage.Columns().FaqCateId)
		md = md.WhereIn(dao.FaqCate.Columns().Id, ids)
	}
	err = md.Page(req.Current, req.PageSize).
		With(model.FaqCateWithLanguage{}.Item). // 预加载一对多
		OrderDesc(dao.FaqCate.Columns().Id).
		ScanAndCount(&cates, &total, false)

	for _, cate := range cates {
		for _, cateL := range cate.Item {
			if cateL.LanguageId == currentLang {
				cate.Title = cateL.Title
			}
		}
		cate.Item = nil
	}
	if err != nil {
		return
	}
	res = &v1.CateListRes{
		List: cates,
		ListRes: v1.ListRes{
			Current: req.Current,
			Offset:  req.Offset,
			Total:   total,
		},
	}
	return

}

func (f *sFaq) QuestionAdd(ctx context.Context, req *v1.QuestionCreateReq) (res *v1.QuestionCreateRes, err error) {

	err = dao.FaqQuestion.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// faq_question表新增。
		lastId, err := tx.Model(dao.FaqQuestion.Table()).Ctx(ctx).Data(
			do.FaqQuestion{Sort: req.Sort, FaqCateId: req.CateId}).InsertAndGetId()
		if err != nil {
			return err
		}
		// faq_question_language 表新增。
		var faqQuestionLanguages = make([]do.FaqQuestionLanguage, 0, len(req.Item))

		isZh := 0
		isEn := 0
		isId := 0

		for _, item := range req.Item {
			faqQuestionLanguage := do.FaqQuestionLanguage{
				FaqQuestionId: lastId,
				LanguageId:    item.LanguageId,
				Title:         item.Title,
				Desc:          item.Desc,
			}
			faqQuestionLanguages = append(faqQuestionLanguages, faqQuestionLanguage)

			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
		}

		_, err = tx.Model(dao.FaqQuestionLanguage.Table()).Ctx(ctx).Data(faqQuestionLanguages).Insert()
		if err != nil {
			return err
		}
		// cate  count 加一
		_, err = tx.Model(dao.FaqCate.Table()).Ctx(ctx).Where("id = ?", req.CateId).
			Increment(dao.FaqCate.Columns().CateCount, 1)
		if err != nil {
			return err
		}
		// 更新主表语言。
		_, err = tx.Model(dao.FaqQuestion.Table()).Ctx(ctx).Where(dao.FaqQuestion.Columns().Id, lastId).
			Update(do.FaqQuestion{
				IsZh: isZh,
				IsEn: isEn,
				IsId: isId,
			})
		if err != nil {
			return err
		}

		// 位置
		faqShowPositions := make([]do.FaqShowPosition, 0, len(req.PositionId))
		for _, positionId := range req.PositionId {
			faqShowPosition := do.FaqShowPosition{
				FaqQuestionId: lastId,
				PositionId:    positionId,
			}
			faqShowPositions = append(faqShowPositions, faqShowPosition)
		}
		_, err = dao.FaqShowPosition.Ctx(ctx).Data(faqShowPositions).Insert()

		return err
	})

	return
}

func (f *sFaq) QuestionEdit(ctx context.Context, req *v1.QuestionEditReq) (res *v1.QuestionEditRes, err error) {
	err = dao.FaqQuestion.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 更新主表
		_, err := tx.Model(dao.FaqQuestion.Table()).
			Data(do.FaqQuestion{Sort: req.Sort, FaqCateId: req.CateId}).
			Where(dao.FaqQuestion.Columns().Id, req.Id).
			Update()
		if err != nil {
			return err
		}
		isZh := 0
		isEn := 0
		isId := 0
		// 2. 处理子表：新增 or 更新
		for _, item := range req.Item {
			faqQuestionLanguage := do.FaqQuestionLanguage{
				FaqQuestionId: req.Id,
				Title:         item.Title,
				Desc:          item.Desc,
				LanguageId:    item.LanguageId,
			}

			//if item.Id > 0 {
			//	// 更新
			//	_, err = tx.Model(dao.FaqQuestionLanguage.Table()).
			//		Data(faqQuestionLanguage).
			//		Where(dao.FaqQuestionLanguage.Columns().Id, item.Id).
			//		Update()
			//} else {
			//	// 新增
			//	_, err = tx.Model(dao.FaqQuestionLanguage.Table()).
			//		Data(faqQuestionLanguage).
			//		Insert()
			//}
			//if err != nil {
			//	return err
			//}

			// save 如果unique 冲突了会更新，否则新增
			_, err = tx.Model(dao.FaqQuestionLanguage.Table()).Data(faqQuestionLanguage).Save()
			if err != nil {
				return err
			}

			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
		}

		// 更新主表语言。
		_, err = tx.Model(dao.FaqQuestion.Table()).Ctx(ctx).Where(dao.FaqQuestion.Columns().Id, req.Id).
			Update(do.FaqQuestion{
				IsZh: isZh,
				IsEn: isEn,
				IsId: isId,
			})
		if err != nil {
			return err
		}

		// 位置先删除
		_, err = tx.Model(dao.FaqShowPosition.Table()).Where(dao.FaqShowPosition.Columns().FaqQuestionId, req.Id).Delete()
		if err != nil {
			return err
		}

		// 位置再更新
		faqShowPositions := make([]do.FaqShowPosition, 0, len(req.PositionId))
		for _, positionId := range req.PositionId {
			faqShowPosition := do.FaqShowPosition{
				FaqQuestionId: req.Id,
				PositionId:    positionId,
			}
			faqShowPositions = append(faqShowPositions, faqShowPosition)
		}
		_, err = dao.FaqShowPosition.Ctx(ctx).Data(faqShowPositions).Insert()

		return err
	})

	return
}

func (f *sFaq) QuestionDelete(ctx context.Context, req *v1.QuestionDeleteReq) (res *v1.QuestionDeleteRes, err error) {
	err = dao.FaqQuestion.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		record, err := tx.Model(dao.FaqQuestion.Table()).Where(dao.FaqQuestion.Columns().Id, req.Id).One()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqQuestion.Table()).Where(dao.FaqQuestion.Columns().Id, req.Id).Delete()

		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqQuestionLanguage.Table()).Where(dao.FaqQuestionLanguage.Columns().FaqQuestionId, req.Id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqCate.Table()).Ctx(ctx).Where("id = ?", record["faq_cate_id"]).
			Decrement(dao.FaqCate.Columns().CateCount, 1)
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqShowPosition.Table()).Where(dao.FaqShowPosition.Columns().FaqQuestionId, req.Id).Delete()

		return err
	})
	return

}

func (f *sFaq) QuestionOne(ctx context.Context, req *v1.QuestionOneReq) (res *v1.QuestionOneRes, err error) {
	var question *model.FaqQuestionWithLanguage
	err = dao.FaqQuestion.Ctx(ctx).Where(dao.FaqQuestion.Columns().Id, req.Id).
		With(model.FaqQuestionWithLanguage{}.Item, model.FaqQuestionWithLanguage{}.ShowPosition).
		Scan(&question)
	if err != nil {
		return
	}
	if question == nil {
		return
	}
	if question.ShowPosition != nil {
		var positionId []int
		temp := gutil.ListItemValuesUnique(question.ShowPosition, "PositionId")
		positionId = gconv.Ints(temp)
		question.PositionId = positionId
		question.ShowPosition = nil
	}

	return &v1.QuestionOneRes{
		question,
	}, nil
}

func (f *sFaq) QuestionList(ctx context.Context, req *v1.QuestionListReq) (res *v1.QuestionListRes, err error) {
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))

	var questions []*model.FaqQuestionWithLanguage
	var total int
	md := dao.FaqQuestion.Ctx(ctx)
	if req.CateId > consts.Zero {
		md = md.Where(dao.FaqQuestion.Columns().FaqCateId, req.CateId)
	}
	if req.Title != "" {
		ids := dao.FaqQuestionLanguage.Ctx(ctx).
			WhereLike(dao.FaqQuestionLanguage.Columns().Title, "%"+req.Title+"%").
			Fields(dao.FaqQuestionLanguage.Columns().FaqQuestionId)
		md = md.WhereIn(dao.FaqQuestion.Columns().Id, ids)
	}
	err = md.Page(req.Current, req.PageSize).
		WithAll(). // 预加载一对多
		OrderDesc(dao.FaqCate.Columns().Id).
		ScanAndCount(&questions, &total, false)
	if err != nil {
		return
	}

	for _, question := range questions {
		for _, questionL := range question.Item {
			if questionL.LanguageId == currentLang {
				question.Title = questionL.Title
			}
		}
		question.Item = nil

		for _, cateL := range question.Cate {
			if cateL.LanguageId == currentLang {
				question.CateTitle = cateL.Title
			}
		}
		question.Cate = nil

		var positionId []int
		temp := gutil.ListItemValuesUnique(question.ShowPosition, "PositionId")
		positionId = gconv.Ints(temp)

		question.PositionId = positionId
		question.ShowPosition = nil

	}

	res = &v1.QuestionListRes{
		List: questions,
		ListRes: v1.ListRes{
			Current: req.Current,
			Offset:  req.Offset,
			Total:   total,
		},
	}
	return
}

func (f *sFaq) Publish(ctx context.Context, req *v1.FaqPublishReq) (res *v1.FaqPublishRes, err error) {
	doData := do.FaqQuestion{
		IsPublish: req.IsPublish,
	}
	if req.IsPublish == consts.FaqStatusPublished {
		doData.PublishTime = gtime.New().UnixMilli()
	}
	_, err = dao.FaqQuestion.Ctx(ctx).WhereIn(dao.FaqQuestion.Columns().Id, req.Ids).
		WhereNot(dao.FaqQuestion.Columns().IsPublish, req.IsPublish).Update(doData)
	if err != nil {
		return nil, err
	}
	return
}
