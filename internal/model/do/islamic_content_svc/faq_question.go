// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// FaqQuestion is the golang structure of table faq_question for DAO operations like Where/Data.
type FaqQuestion struct {
	g.Meta        `orm:"table:faq_question, do:true"`
	Id            interface{} //
	IsZh          interface{} // 是否中文，0-否，1-是
	IsEn          interface{} // 是否英文，0-否，1-是
	IsId          interface{} // 是否印尼文，0-否，1-是
	FaqCateId     interface{} // 名言分类id
	IsPublish     interface{} // 发布状态 [ 0未发布 1已发布  2 已下线]
	Sort          interface{} // 排序
	PublishTime   interface{} // 发布时间(毫秒时间戳)
	Views         interface{} // 浏览量
	CreateAccount interface{} // 创建者
	UpdateAccount interface{} // 更新者
	CreateTime    interface{} // 创建时间(毫秒时间戳)
	UpdateTime    interface{} // 更新时间(毫秒时间戳)
	DeleteTime    interface{} //
}
