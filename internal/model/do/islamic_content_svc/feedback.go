// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Feedback is the golang structure of table feedback for DAO operations like Where/Data.
type Feedback struct {
	g.Meta           `orm:"table:feedback, do:true"`
	Id               interface{} //
	FeedbackType     interface{} // 反馈类型  1 系统故障 2 功能建议
	UserId           interface{} // 用户id
	UserAccount      interface{} // 用户账号
	Images           interface{} // 反馈图片 json字符串
	Desc             interface{} // 反馈内容
	FeedbackTime     interface{} // 反馈时间ms
	FeedbackStatus   interface{} // 反馈状态 1 未处理 2已处理
	FeedbackResult   interface{} // 反馈结果 1 无效反馈 2有效反馈
	CompleteTime     interface{} // 处理时间ms
	CompleteRemark   interface{} // 处理备注
	CompleteAccount  interface{} // 处理人账号
	CompleteNickName interface{} // 处理人昵称
	CreateTime       interface{} // 创建时间(毫秒时间戳)
	UpdateTime       interface{} // 更新时间(毫秒时间戳)
	DeleteTime       interface{} // 删除时间
}
