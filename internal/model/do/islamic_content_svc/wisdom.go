// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Wisdom is the golang structure of table wisdom for DAO operations like Where/Data.
type Wisdom struct {
	g.Meta          `orm:"table:wisdom, do:true"`
	Id              interface{} //
	IsZh            interface{} // 是否中文，0-否，1-是
	IsEn            interface{} // 是否英文，0-否，1-是
	IsId            interface{} // 是否印尼文，0-否，1-是
	WisdomCateId    interface{} // 分类id
	IsPublish       interface{} // 状态 [ 0未发布 1 已发布  2 已下线]
	PublishTime     interface{} // 发布时间(毫秒时间戳)
	Sort            interface{} // 排序
	Views           interface{} // 浏览量
	Shares          interface{} // 分享量
	Collects        interface{} // 收藏量
	ArticleId       interface{} // 文章id
	ArticleCategory interface{} // 文章分类id
	CreateAccount   interface{} // 创建者
	UpdateAccount   interface{} // 更新者
	CreateTime      interface{} // 创建时间(毫秒时间戳)
	UpdateTime      interface{} // 更新时间(毫秒时间戳)
	DeleteTime      interface{} //
}
