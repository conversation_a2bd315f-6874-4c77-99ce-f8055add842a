// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// WisdomCate is the golang structure of table wisdom_cate for DAO operations like Where/Data.
type WisdomCate struct {
	g.Meta        `orm:"table:wisdom_cate, do:true"`
	Id            interface{} //
	IsZh          interface{} // 是否中文，0-否，1-是
	IsEn          interface{} // 是否英文，0-否，1-是
	IsId          interface{} // 是否印尼文，0-否，1-是
	IsPublish     interface{} // 状态 [ 0未发布 1 已发布  2 已下线]
	Sort          interface{} // 排序
	CateCount     interface{} // 分类下的文章总数
	Remark        interface{} // 备注
	CreateAccount interface{} // 创建者
	UpdateAccount interface{} // 更新者
	CreateTime    interface{} // 创建时间(毫秒时间戳)
	UpdateTime    interface{} // 更新时间(毫秒时间戳)
	DeleteTime    interface{} //
}
