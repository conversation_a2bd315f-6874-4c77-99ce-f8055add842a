// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// WisdomLanguage is the golang structure of table wisdom_language for DAO operations like Where/Data.
type WisdomLanguage struct {
	g.Meta     `orm:"table:wisdom_language, do:true"`
	Id         interface{} // 主键ID
	WisdomId   interface{} // 名言id
	LanguageId interface{} // 语言id
	Title      interface{} // 名言标题
	ImageUrl   interface{} // 名言图片url
}
