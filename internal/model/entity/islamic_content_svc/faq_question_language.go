// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// FaqQuestionLanguage is the golang structure for table faq_question_language.
type FaqQuestionLanguage struct {
	Id            uint   `json:"id"            orm:"id"              description:""`
	FaqQuestionId uint   `json:"faqQuestionId" orm:"faq_question_id" description:"faq问题id"`
	LanguageId    int    `json:"languageId"    orm:"language_id"     description:"语言id"`
	Title         string `json:"title"         orm:"title"           description:"标题"`
	Desc          string `json:"desc"          orm:"desc"            description:"详情"`
}
