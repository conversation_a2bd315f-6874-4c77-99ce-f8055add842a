// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// WisdomCate is the golang structure for table wisdom_cate.
type WisdomCate struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	IsZh          uint   `json:"isZh"          orm:"is_zh"          description:"是否中文，0-否，1-是"`
	IsEn          uint   `json:"isEn"          orm:"is_en"          description:"是否英文，0-否，1-是"`
	IsId          uint   `json:"isId"          orm:"is_id"          description:"是否印尼文，0-否，1-是"`
	IsPublish     int    `json:"isPublish"     orm:"is_publish"     description:"状态 [ 0未发布 1 已发布  2 已下线]"`
	Sort          int    `json:"sort"          orm:"sort"           description:"排序"`
	CateCount     int    `json:"cateCount"     orm:"cate_count"     description:"分类下的文章总数"`
	Remark        string `json:"remark"        orm:"remark"         description:"备注"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间(毫秒时间戳)"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间(毫秒时间戳)"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:""`
}
