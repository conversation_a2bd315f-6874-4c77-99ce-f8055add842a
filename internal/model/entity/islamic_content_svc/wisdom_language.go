// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// WisdomLanguage is the golang structure for table wisdom_language.
type WisdomLanguage struct {
	Id         uint   `json:"id"         orm:"id"          description:"主键ID"`
	WisdomId   uint   `json:"wisdomId"   orm:"wisdom_id"   description:"名言id"`
	LanguageId int    `json:"languageId" orm:"language_id" description:"语言id"`
	Title      string `json:"title"      orm:"title"       description:"名言标题"`
	ImageUrl   string `json:"imageUrl"   orm:"image_url"   description:"名言图片url"`
}
